import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import { Alert } from '../components/ui';
import {
  FieldOfficerDashboard,
  BranchManagerDashboard,
  AdminDashboard,
  MemberDashboard
} from '../components/dashboard';

export const Dashboard: React.FC = () => {
  const { user, hasRole, hasPermission } = useAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Alert type="warning" title="Access Required" description="Please log in to access the dashboard." />
      </div>
    );
  }

  const getDashboardContent = () => {
    switch (user.role) {
      case UserRole.ADMIN:
        return <AdminDashboard />;
      case UserRole.MANAGER:
        return <BranchManagerDashboard />;
      case UserRole.FIELD_OFFICER:
        return <FieldOfficerDashboard />;
      case UserRole.MEMBER:
        return <MemberDashboard />;
      default:
        return (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg mb-4">🚧 Dashboard not available for your role</div>
            <p className="text-gray-400">Please contact your administrator for access.</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header - Only show for non-member roles or if member wants to see it */}
      {user.role !== UserRole.MEMBER && (
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 dark:from-primary-700 dark:to-primary-800 rounded-xl p-6 text-white">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">
            Welcome back, {user.name}!
          </h1>
          <p className="text-primary-100 text-sm md:text-base">
            Role: {user.role.replace('_', ' ').toUpperCase()}
            {user.memberId && ` • Member ID: ${user.memberId}`}
          </p>
        </div>
      )}

      {/* Role-specific Dashboard Content */}
      {getDashboardContent()}
    </div>
  );
};