import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '../contexts/AuthContext';
import { LoginCredentials, UserRole } from '../types';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent,
  Button, 
  Input, 
  Alert,
  Loading,
  Modal,
  Form,
  FormGroup,
  FormLabel
} from '../components/ui';

// Validation schema
const loginSchema = z.object({
  identifier: z.string()
    .min(1, 'Member ID or Email is required')
    .refine((val) => {
      // Check if it's an email or member ID
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const memberIdRegex = /^[A-Z]{3}\d{3,}$/; // Format: MEM001, ADM001, etc.
      return emailRegex.test(val) || memberIdRegex.test(val) || val.length >= 3;
    }, 'Please enter a valid email or Member ID'),
  password: z.string()
    .min(6, 'Password must be at least 6 characters')
    .max(100, 'Password is too long'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

// Password strength checker
const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
  let score = 0;
  if (password.length >= 8) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[^a-zA-Z\d]/.test(password)) score++;

  const levels = [
    { score: 0, label: 'Very Weak', color: 'bg-danger-500' },
    { score: 1, label: 'Weak', color: 'bg-danger-400' },
    { score: 2, label: 'Fair', color: 'bg-warning-500' },
    { score: 3, label: 'Good', color: 'bg-warning-400' },
    { score: 4, label: 'Strong', color: 'bg-success-500' },
    { score: 5, label: 'Very Strong', color: 'bg-success-600' },
  ];

  return levels[score] || levels[0];
};

export const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: '' });

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    reset,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
  });

  const watchedPassword = watch('password', '');

  useEffect(() => {
    if (watchedPassword) {
      setPasswordStrength(getPasswordStrength(watchedPassword));
    }
  }, [watchedPassword]);

  // Check if user is already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [navigate, location, isAuthenticated]);

  const getRoleBasedRedirect = (role: UserRole): string => {
    switch (role) {
      case UserRole.ADMIN:
        return '/admin/dashboard';
      case UserRole.MANAGER:
        return '/manager/dashboard';
      case UserRole.FIELD_OFFICER:
        return '/field-officer/dashboard';
      case UserRole.MEMBER:
        return '/member/dashboard';
      default:
        return '/dashboard';
    }
  };

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await login(data);

      // Store additional session info
      if (data.rememberMe) {
        localStorage.setItem('sessionId', result.sessionId);
        localStorage.setItem('tokenExpiresAt', result.expiresAt);
      }

      // Role-based redirection
      const redirectPath = getRoleBasedRedirect(result.user.role);
      const from = (location.state as any)?.from?.pathname || redirectPath;

      navigate(from, { replace: true });
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
      setLoginAttempts(prev => prev + 1);
      
      // Reset form on multiple failed attempts
      if (loginAttempts >= 2) {
        reset();
        setLoginAttempts(0);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (email: string) => {
    try {
      // TODO: Implement forgot password functionality
      console.log('Forgot password for:', email);
      setShowForgotPassword(false);
      // Show success message
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-secondary-900 dark:text-secondary-100">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-secondary-600 dark:text-secondary-400">
            Enter your Member ID or email address
          </p>
        </div>

        {/* Login Form */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Login</CardTitle>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert
                type="danger"
                title="Login Failed"
                description={error}
                dismissible
                onDismiss={() => setError(null)}
                className="mb-4"
              />
            )}

            <Form onSubmit={handleSubmit(onSubmit)} spacing="md">
              <FormGroup>
                <FormLabel htmlFor="identifier" required>
                  Member ID or Email
                </FormLabel>
                <Input
                  id="identifier"
                  type="text"
                  placeholder="Enter Member ID (e.g., MEM001) or email"
                  error={errors.identifier?.message}
                  leftIcon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  }
                  {...register('identifier')}
                />
              </FormGroup>

              <FormGroup>
                <FormLabel htmlFor="password" required>
                  Password
                </FormLabel>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  error={errors.password?.message}
                  showPasswordToggle
                  leftIcon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  }
                  {...register('password')}
                />
                
                {/* Password Strength Indicator */}
                {watchedPassword && (
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-secondary-600 dark:text-secondary-400">
                        Password strength: {passwordStrength.label}
                      </span>
                    </div>
                    <div className="mt-1 h-1 bg-secondary-200 dark:bg-secondary-700 rounded-full overflow-hidden">
                      <div
                        className={`h-full transition-all duration-300 ${passwordStrength.color}`}
                        style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                      />
                    </div>
                  </div>
                )}
              </FormGroup>

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                    {...register('rememberMe')}
                  />
                  <span className="ml-2 text-sm text-secondary-700 dark:text-secondary-300">
                    Remember me
                  </span>
                </label>

                <button
                  type="button"
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
                >
                  Forgot password?
                </button>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                loading={isLoading}
                disabled={!isValid || isLoading}
              >
                {isLoading ? 'Signing in...' : 'Sign in'}
              </Button>

              {/* Login Attempts Warning */}
              {loginAttempts > 0 && (
                <Alert
                  type="warning"
                  description={`Failed login attempts: ${loginAttempts}/3. Account will be temporarily locked after 3 failed attempts.`}
                  className="mt-4"
                />
              )}
            </Form>
          </CardContent>
        </Card>

        {/* Advertisement Display Box */}
        <Card variant="outlined" className="mt-6">
          <CardContent className="text-center py-8">
            <div className="text-secondary-500 dark:text-secondary-400">
              <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
              <p className="text-sm">
                Advertisement space managed by admin
              </p>
              <p className="text-xs mt-2 text-secondary-400">
                Special offers and announcements will appear here
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer Links */}
        <div className="text-center">
          <p className="text-sm text-secondary-600 dark:text-secondary-400">
            Don't have an account?{' '}
            <Link
              to="/register"
              className="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
            >
              Contact your branch manager
            </Link>
          </p>
        </div>
      </div>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={showForgotPassword}
        onClose={() => setShowForgotPassword(false)}
        onSubmit={handleForgotPassword}
      />
    </div>
  );
};

// Forgot Password Modal Component
interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (email: string) => void;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    try {
      await onSubmit(email);
      setEmail('');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Reset Password"
      description="Enter your email address and we'll send you a link to reset your password."
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          type="email"
          label="Email Address"
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
        
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={isLoading}
            disabled={!email || isLoading}
          >
            Send Reset Link
          </Button>
        </div>
      </form>
    </Modal>
  );
};
