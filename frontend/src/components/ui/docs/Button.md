# Button Component

A versatile button component with multiple variants, sizes, and states.

## Import

```tsx
import { Button } from '../components/ui';
```

## Basic Usage

```tsx
<Button>Default Button</Button>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'primary' \| 'secondary' \| 'danger' \| 'outline' \| 'ghost' \| 'link'` | `'primary'` | Button style variant |
| `size` | `'sm' \| 'md' \| 'lg' \| 'xl'` | `'md'` | Button size |
| `loading` | `boolean` | `false` | Shows loading spinner |
| `leftIcon` | `ReactNode` | - | Icon to display on the left |
| `rightIcon` | `ReactNode` | - | Icon to display on the right |
| `fullWidth` | `boolean` | `false` | Makes button full width |
| `disabled` | `boolean` | `false` | Disables the button |
| `children` | `ReactNode` | - | Button content |

## Variants

### Primary
```tsx
<Button variant="primary">Primary Button</Button>
```

### Secondary
```tsx
<Button variant="secondary">Secondary Button</Button>
```

### Danger
```tsx
<Button variant="danger">Danger Button</Button>
```

### Outline
```tsx
<Button variant="outline">Outline Button</Button>
```

### Ghost
```tsx
<Button variant="ghost">Ghost Button</Button>
```

### Link
```tsx
<Button variant="link">Link Button</Button>
```

## Sizes

```tsx
<Button size="sm">Small</Button>
<Button size="md">Medium</Button>
<Button size="lg">Large</Button>
<Button size="xl">Extra Large</Button>
```

## With Icons

```tsx
<Button 
  leftIcon={<PlusIcon />}
  variant="primary"
>
  Add Item
</Button>

<Button 
  rightIcon={<ArrowRightIcon />}
  variant="outline"
>
  Continue
</Button>
```

## Loading State

```tsx
<Button loading>Loading...</Button>
```

## Full Width

```tsx
<Button fullWidth variant="primary">
  Full Width Button
</Button>
```

## Disabled State

```tsx
<Button disabled>Disabled Button</Button>
```

## Custom Styling

```tsx
<Button 
  variant="primary"
  className="bg-purple-600 hover:bg-purple-700"
>
  Custom Purple Button
</Button>
```

## Accessibility

- Supports keyboard navigation (Enter and Space keys)
- Proper ARIA attributes for screen readers
- Focus indicators for keyboard users
- Disabled state properly communicated to assistive technologies

## Examples

### Form Submit Button
```tsx
<form onSubmit={handleSubmit}>
  <Button 
    type="submit" 
    variant="primary" 
    loading={isSubmitting}
    fullWidth
  >
    {isSubmitting ? 'Submitting...' : 'Submit Form'}
  </Button>
</form>
```

### Action Buttons
```tsx
<div className="flex space-x-3">
  <Button variant="outline" onClick={handleCancel}>
    Cancel
  </Button>
  <Button variant="danger" onClick={handleDelete}>
    Delete
  </Button>
</div>
```

### Icon Buttons
```tsx
<div className="flex space-x-2">
  <Button 
    size="sm" 
    variant="ghost"
    leftIcon={<EditIcon />}
  >
    Edit
  </Button>
  <Button 
    size="sm" 
    variant="ghost"
    leftIcon={<TrashIcon />}
  >
    Delete
  </Button>
</div>
```
