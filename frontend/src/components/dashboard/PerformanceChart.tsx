import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui';
import { PerformanceData, ChartDataPoint } from '../../types';

interface PerformanceChartProps {
  data: PerformanceData[] | ChartDataPoint[];
  title: string;
  type?: 'line' | 'bar' | 'pie';
  className?: string;
  height?: number;
}

export const PerformanceChart: React.FC<PerformanceChartProps> = ({ 
  data, 
  title,
  type = 'bar',
  className = '',
  height = 300
}) => {
  // Simple chart implementation using CSS and HTML
  // In a production app, you'd use a proper charting library like Chart.js or Recharts
  
  const renderBarChart = (chartData: PerformanceData[]) => {
    const maxValue = Math.max(...chartData.map(d => Math.max(d.collections, d.disbursements)));
    
    return (
      <div className="space-y-4">
        {chartData.map((item, index) => (
          <div key={index} className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="font-medium text-gray-700 dark:text-gray-300">{item.period}</span>
              <div className="flex gap-4 text-xs">
                <span className="text-green-600">Collections: ৳{item.collections.toLocaleString()}</span>
                <span className="text-blue-600">Disbursements: ৳{item.disbursements.toLocaleString()}</span>
              </div>
            </div>
            <div className="flex gap-1 h-8">
              <div 
                className="bg-green-500 rounded-l"
                style={{ width: `${(item.collections / maxValue) * 100}%` }}
                title={`Collections: ৳${item.collections.toLocaleString()}`}
              ></div>
              <div 
                className="bg-blue-500 rounded-r"
                style={{ width: `${(item.disbursements / maxValue) * 100}%` }}
                title={`Disbursements: ৳${item.disbursements.toLocaleString()}`}
              ></div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderPieChart = (chartData: ChartDataPoint[]) => {
    const total = chartData.reduce((sum, item) => sum + item.value, 0);
    
    return (
      <div className="flex items-center justify-center">
        <div className="relative w-48 h-48">
          {/* Simple pie chart using CSS */}
          <div className="w-full h-full rounded-full bg-gray-200 dark:bg-gray-700 relative overflow-hidden">
            {chartData.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500'];
              
              return (
                <div
                  key={index}
                  className={`absolute inset-0 ${colors[index % colors.length]}`}
                  style={{
                    clipPath: `polygon(50% 50%, 50% 0%, ${50 + percentage * 0.5}% 0%, 50% 50%)`
                  }}
                  title={`${item.label}: ${item.value} (${percentage.toFixed(1)}%)`}
                ></div>
              );
            })}
          </div>
          
          {/* Legend */}
          <div className="absolute -right-32 top-0 space-y-2">
            {chartData.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500'];
              
              return (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <div className={`w-3 h-3 rounded ${colors[index % colors.length]}`}></div>
                  <span className="text-gray-700 dark:text-gray-300">
                    {item.label}: {percentage.toFixed(1)}%
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const renderLineChart = (chartData: PerformanceData[]) => {
    const maxValue = Math.max(...chartData.map(d => Math.max(d.collections, d.disbursements)));
    
    return (
      <div className="relative" style={{ height: `${height}px` }}>
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>৳{maxValue.toLocaleString()}</span>
          <span>৳{(maxValue * 0.75).toLocaleString()}</span>
          <span>৳{(maxValue * 0.5).toLocaleString()}</span>
          <span>৳{(maxValue * 0.25).toLocaleString()}</span>
          <span>৳0</span>
        </div>
        
        {/* Chart area */}
        <div className="ml-16 h-full border-l border-b border-gray-200 dark:border-gray-700 relative">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
            <div
              key={index}
              className="absolute w-full border-t border-gray-100 dark:border-gray-800"
              style={{ bottom: `${ratio * 100}%` }}
            ></div>
          ))}
          
          {/* Data points and lines */}
          <div className="flex justify-between items-end h-full px-4">
            {chartData.map((item, index) => (
              <div key={index} className="flex flex-col items-center space-y-2">
                {/* Collections bar */}
                <div
                  className="w-4 bg-green-500 rounded-t"
                  style={{ height: `${(item.collections / maxValue) * 100}%` }}
                  title={`Collections: ৳${item.collections.toLocaleString()}`}
                ></div>
                {/* Disbursements bar */}
                <div
                  className="w-4 bg-blue-500 rounded-t"
                  style={{ height: `${(item.disbursements / maxValue) * 100}%` }}
                  title={`Disbursements: ৳${item.disbursements.toLocaleString()}`}
                ></div>
                {/* Period label */}
                <span className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                  {item.period}
                </span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Legend */}
        <div className="flex justify-center gap-6 mt-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-sm text-gray-700 dark:text-gray-300">Collections</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span className="text-sm text-gray-700 dark:text-gray-300">Disbursements</span>
          </div>
        </div>
      </div>
    );
  };

  const renderChart = () => {
    if (type === 'pie' && 'label' in data[0]) {
      return renderPieChart(data as ChartDataPoint[]);
    } else if (type === 'line' && 'period' in data[0]) {
      return renderLineChart(data as PerformanceData[]);
    } else if ('period' in data[0]) {
      return renderBarChart(data as PerformanceData[]);
    }
    
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        <span className="text-4xl mb-2 block">📊</span>
        <p>Chart data not available</p>
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>📈</span>
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <span className="text-4xl mb-2 block">📊</span>
            <p>No data available</p>
          </div>
        ) : (
          renderChart()
        )}
      </CardContent>
    </Card>
  );
};

export default PerformanceChart;
