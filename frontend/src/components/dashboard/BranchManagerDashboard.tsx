import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Loading } from '../ui';
import { MetricCard, QuickActionButton, ActivityTimeline, PerformanceChart } from './';
import { BranchManagerDashboardData, DashboardMetric } from '../../types';
import DashboardService from '../../services/dashboardService';

export const BranchManagerDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<BranchManagerDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await DashboardService.getBranchManagerDashboard();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error('Error fetching branch manager dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          ))}
        </div>
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-4">⚠️ {error || 'Failed to load dashboard'}</div>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const { metrics, installmentStatus, fieldOfficerPerformance, recentActivities, quickActions } = dashboardData;

  // Convert metrics to DashboardMetric format
  const dashboardMetrics: DashboardMetric[] = [
    {
      label: 'Field Officers in Branch',
      value: metrics.totalFieldOfficers,
      icon: 'users',
      color: 'primary'
    },
    {
      label: 'Total Active Loans',
      value: metrics.totalActiveLoans,
      icon: 'credit-card',
      color: 'success'
    },
    {
      label: 'Total Members',
      value: metrics.totalMembers,
      icon: 'users',
      color: 'info'
    },
    {
      label: 'Collection Achievement',
      value: `${((metrics.collectedAmount / metrics.monthlyCollectionTarget) * 100).toFixed(1)}%`,
      icon: 'trending-up',
      color: (metrics.collectedAmount / metrics.monthlyCollectionTarget) >= 1 ? 'success' : 
             (metrics.collectedAmount / metrics.monthlyCollectionTarget) >= 0.8 ? 'warning' : 'danger'
    }
  ];

  const totalInstallments = installmentStatus.pending + installmentStatus.overdue + installmentStatus.collected;

  return (
    <div className="space-y-6">
      {/* Branch Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardMetrics.map((metric, index) => (
          <MetricCard key={index} metric={metric} />
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>⚡</span>
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <QuickActionButton key={action.id} action={action} size="sm" />
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Installment Collection Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>📊</span>
              Installment Collection Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Status Overview */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {installmentStatus.collected}
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300">Collected</div>
                </div>
                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                    {installmentStatus.pending}
                  </div>
                  <div className="text-sm text-yellow-700 dark:text-yellow-300">Pending</div>
                </div>
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {installmentStatus.overdue}
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300">Overdue</div>
                </div>
              </div>

              {/* Progress Bars */}
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-green-600 dark:text-green-400">Collected</span>
                    <span>{((installmentStatus.collected / totalInstallments) * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${(installmentStatus.collected / totalInstallments) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-yellow-600 dark:text-yellow-400">Pending</span>
                    <span>{((installmentStatus.pending / totalInstallments) * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-yellow-500 h-2 rounded-full"
                      style={{ width: `${(installmentStatus.pending / totalInstallments) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-red-600 dark:text-red-400">Overdue</span>
                    <span>{((installmentStatus.overdue / totalInstallments) * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-red-500 h-2 rounded-full"
                      style={{ width: `${(installmentStatus.overdue / totalInstallments) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Branch Financial Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>💰</span>
              Branch Financial Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-xl font-bold text-green-600 dark:text-green-400">
                    ৳{metrics.branchIncome.toLocaleString()}
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300">Total Income</div>
                </div>
                <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-xl font-bold text-red-600 dark:text-red-400">
                    ৳{metrics.branchExpenses.toLocaleString()}
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300">Total Expenses</div>
                </div>
              </div>
              
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  ৳{(metrics.branchIncome - metrics.branchExpenses).toLocaleString()}
                </div>
                <div className="text-sm text-blue-700 dark:text-blue-300">Net Profit</div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Collection Target</span>
                  <span>৳{metrics.monthlyCollectionTarget.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Collected Amount</span>
                  <span>৳{metrics.collectedAmount.toLocaleString()}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div 
                    className="bg-primary-500 h-3 rounded-full"
                    style={{ width: `${Math.min((metrics.collectedAmount / metrics.monthlyCollectionTarget) * 100, 100)}%` }}
                  ></div>
                </div>
                <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                  {((metrics.collectedAmount / metrics.monthlyCollectionTarget) * 100).toFixed(1)}% of target achieved
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Field Officer Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>👥</span>
              Field Officer Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {fieldOfficerPerformance.map((officer) => (
                <div key={officer.id} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">{officer.name}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      officer.targetAchievement >= 100 ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      officer.targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                      'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}>
                      {officer.targetAchievement.toFixed(1)}%
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div>Members: {officer.membersCount}</div>
                    <div>Collections: ৳{officer.collectionsThisMonth.toLocaleString()}</div>
                  </div>
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          officer.targetAchievement >= 100 ? 'bg-green-500' :
                          officer.targetAchievement >= 80 ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(officer.targetAchievement, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <ActivityTimeline 
          activities={recentActivities} 
          title="Recent Branch Activities"
          maxItems={5}
        />
      </div>
    </div>
  );
};

export default BranchManagerDashboard;
