import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Loading } from '../ui';
import { MetricCard, QuickActionButton, ActivityTimeline } from './';
import { FieldOfficerDashboardData, DashboardMetric, InstallmentSummary } from '../../types';
import DashboardService from '../../services/dashboardService';

export const FieldOfficerDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<FieldOfficerDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await DashboardService.getFieldOfficerDashboard();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error('Error fetching field officer dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          ))}
        </div>
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-4">⚠️ {error || 'Failed to load dashboard'}</div>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const { metrics, recentActivities, quickActions, upcomingCollections } = dashboardData;

  // Convert metrics to DashboardMetric format
  const dashboardMetrics: DashboardMetric[] = [
    {
      label: 'Total Members Under Supervision',
      value: metrics.totalMembers,
      icon: 'users',
      color: 'primary'
    },
    {
      label: 'Total Collections Made',
      value: metrics.totalCollections,
      icon: 'dollar-sign',
      color: 'success',
      change: metrics.achievementPercentage - 100,
      changeType: metrics.achievementPercentage >= 100 ? 'increase' : 'decrease'
    },
    {
      label: 'Monthly Target Achievement',
      value: `${metrics.achievementPercentage.toFixed(1)}%`,
      icon: 'trending-up',
      color: metrics.achievementPercentage >= 100 ? 'success' : metrics.achievementPercentage >= 80 ? 'warning' : 'danger'
    },
    {
      label: 'Pending Installments',
      value: metrics.pendingInstallments,
      icon: 'clock',
      color: 'warning'
    }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD', {
      day: 'numeric',
      month: 'short'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'overdue':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardMetrics.map((metric, index) => (
          <MetricCard key={index} metric={metric} />
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>⚡</span>
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <QuickActionButton key={action.id} action={action} size="sm" />
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Collections */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>📅</span>
              Upcoming Collections
            </CardTitle>
          </CardHeader>
          <CardContent>
            {upcomingCollections.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <span className="text-4xl mb-2 block">✅</span>
                <p>No upcoming collections</p>
              </div>
            ) : (
              <div className="space-y-3">
                {upcomingCollections.slice(0, 5).map((collection) => (
                  <div key={collection.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {collection.memberName}
                        </h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(collection.status)}`}>
                          {collection.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        ID: {collection.memberId} • Installment #{collection.installmentNo}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900 dark:text-white">
                        ৳{collection.amount.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Due: {formatDate(collection.dueDate)}
                      </p>
                    </div>
                  </div>
                ))}
                {upcomingCollections.length > 5 && (
                  <div className="text-center pt-2">
                    <button className="text-sm text-primary-600 dark:text-primary-400 hover:underline">
                      View all {upcomingCollections.length} collections
                    </button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <ActivityTimeline 
          activities={recentActivities} 
          title="Recent Activities"
          maxItems={5}
        />
      </div>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>📊</span>
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                ৳{metrics.totalCollections.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Collections This Month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                ৳{metrics.monthlyTarget.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Monthly Target</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                metrics.achievementPercentage >= 100 ? 'text-green-600 dark:text-green-400' : 
                metrics.achievementPercentage >= 80 ? 'text-yellow-600 dark:text-yellow-400' : 
                'text-red-600 dark:text-red-400'
              }`}>
                {metrics.achievementPercentage.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Achievement Rate</div>
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="mt-6">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
              <span>Progress to Target</span>
              <span>{metrics.achievementPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-500 ${
                  metrics.achievementPercentage >= 100 ? 'bg-green-500' : 
                  metrics.achievementPercentage >= 80 ? 'bg-yellow-500' : 
                  'bg-red-500'
                }`}
                style={{ width: `${Math.min(metrics.achievementPercentage, 100)}%` }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerDashboard;
